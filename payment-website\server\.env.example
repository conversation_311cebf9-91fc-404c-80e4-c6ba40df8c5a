# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=payshop_db
DB_USER=postgres
DB_PASSWORD=your_password

# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret
RAZORPAY_UPI_ID=merchant@upi

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# Security
JWT_SECRET=your_jwt_secret_key_here

# Email Configuration (for payment confirmations)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=PayShop <<EMAIL>>

# SMS Configuration (optional - for payment confirmations)
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=PAYSHOP

# Payment Configuration
PAYMENT_TIMEOUT_MINUTES=15
PAYMENT_RETRY_ATTEMPTS=3

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_WEBHOOK_NOTIFICATIONS=true

# Company Information
COMPANY_NAME=PayShop
COMPANY_EMAIL=<EMAIL>
COMPANY_PHONE=+91-9876543210
COMPANY_ADDRESS=123 Business Street, Mumbai, Maharashtra 400001
